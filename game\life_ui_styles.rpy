################################################################################
## LIFE游戏UI样式定义
################################################################################

## 颜色主题定义 ####################################################################

# 主色调
define life_colors = {
    "primary_blue": "#87CEEB",      # 天蓝色 - 主要背景色
    "light_blue": "#E8F4FD",       # 浅蓝色 - 面板背景
    "white": "#FFFFFF",            # 白色 - 文本背景
    "light_gray": "#F0F8FF",       # 浅灰蓝 - 功能栏背景
    "text_dark": "#333333",        # 深色文本
    "text_light": "#FFFFFF",       # 浅色文本
    "text_gray": "#666666",        # 灰色文本
    
    # 功能按钮颜色
    "btn_red": "#FF6B6B",          # 红色按钮
    "btn_teal": "#4ECDC4",         # 青色按钮
    "btn_blue": "#45B7D1",         # 蓝色按钮
    "btn_yellow": "#FFD93D",       # 黄色按钮
    "btn_green": "#96CEB4",        # 绿色按钮
    "btn_purple": "#DDA0DD",       # 紫色按钮
    "btn_orange": "#F4A460",       # 橙色按钮
    "btn_light_green": "#90EE90",  # 浅绿色按钮
}

## 通用样式定义 ####################################################################

# 主界面框架样式
style life_main_frame:
    background life_colors["primary_blue"]
    padding (10, 10, 10, 10)

# 状态栏样式
style life_status_bar_frame:
    background life_colors["primary_blue"]
    xsize 1920
    ysize 80
    xalign 0.5
    yalign 0.0

style life_status_text:
    color life_colors["text_light"]
    size 14

style life_status_title:
    color life_colors["text_light"]
    size 16

# 角色面板样式
style life_character_panel_frame:
    background life_colors["light_blue"]
    xsize 180
    ysize 500
    xalign 0.0
    yalign 0.5

style life_character_avatar:
    background life_colors["white"]
    xsize 140
    ysize 140

style life_character_info_text:
    color life_colors["text_dark"]
    size 12

style life_character_time_text:
    color life_colors["text_gray"]
    size 10

# 角色列表样式
style life_character_list_frame:
    background life_colors["light_blue"]
    xsize 120
    ysize 600
    xalign 1.0
    yalign 0.5

style life_character_list_avatar:
    xsize 80
    ysize 80

style life_character_list_label:
    background life_colors["primary_blue"]
    xsize 80
    ysize 25

style life_character_list_text:
    color life_colors["text_light"]
    size 10

# 功能栏样式
style life_function_bar_frame:
    background life_colors["light_gray"]
    xsize 1000
    ysize 120
    xalign 0.5
    yalign 1.0

style life_function_button:
    xsize 70
    ysize 70

style life_function_button_text:
    color life_colors["text_light"]
    size 8

style life_function_label_text:
    color life_colors["text_dark"]
    size 12

# 快捷按钮样式
style life_quick_button:
    background life_colors["btn_light_green"]
    xsize 120
    ysize 35

style life_quick_button_text:
    size 12

## 动画和转场效果 ##################################################################

# 按钮悬停效果
transform life_button_hover:
    on hover:
        easein 0.2 zoom 1.1
    on idle:
        easein 0.2 zoom 1.0

# 面板淡入效果
transform life_panel_fadein:
    alpha 0.0
    easein 0.5 alpha 1.0

# 状态栏滑入效果
transform life_status_bar_slidein:
    yoffset -80
    easein 0.3 yoffset 0

## 自定义UI组件 ####################################################################

# 资源显示组件
screen life_resource_display(icon, value, color="#FFFFFF"):
    hbox:
        spacing 5
        text icon size 16
        text str(value) size 14 color color

# 角色头像组件
screen life_character_avatar(name, color, size=80):
    frame:
        xsize size
        ysize size
        background color
        
        vbox:
            xalign 0.5
            yalign 0.5
            text "头像" size 10 color life_colors["text_light"]
            if size > 60:
                text name size 8 color life_colors["text_light"]

# 功能按钮组件
screen life_function_button(name, icon, color, action=NullAction()):
    vbox:
        spacing 8
        
        # 圆形按钮
        frame:
            style "life_function_button"
            background color
            at life_button_hover
            
            button:
                action action
                
                vbox:
                    xalign 0.5
                    yalign 0.5
                    spacing 2
                    
                    # 图标
                    text icon size 20
                    # 功能名称（小字）
                    text name style "life_function_button_text"
        
        # 按钮标签（底部）
        text name style "life_function_label_text" xalign 0.5

## 响应式设计支持 ##################################################################

# 小屏幕适配
init python:
    def get_screen_scale():
        """获取屏幕缩放比例"""
        if renpy.variant("small"):
            return 0.8
        return 1.0
    
    def get_ui_size(base_size):
        """根据屏幕大小调整UI尺寸"""
        return int(base_size * get_screen_scale())

# 小屏幕样式覆盖
style life_main_frame:
    variant "small"
    padding (5, 5, 5, 5)

style life_status_bar_frame:
    variant "small"
    xsize 1280
    ysize 60

style life_character_panel_frame:
    variant "small"
    xsize 150
    ysize 400

style life_character_list_frame:
    variant "small"
    xsize 100
    ysize 500

style life_function_bar_frame:
    variant "small"
    xsize 800
    ysize 100

style life_function_button:
    variant "small"
    xsize 50
    ysize 50
