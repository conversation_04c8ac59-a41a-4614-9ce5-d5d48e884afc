# LIFE MVP版本技术栈实现指南

## 概述

本文档详细描述了LIFE游戏MVP版本的技术栈选择、架构设计和实现方案。MVP版本专注于核心功能的快速实现，同时保持良好的扩展性为后续迭代做准备。

## 技术栈选择

### 核心技术栈
```yaml
游戏引擎: Renpy 8.4.3
编程语言: Python 3.11+
数据库: SQLite 3.x + SQLAlchemy 2.0+
AI集成: OpenAI API / Ollama本地部署
版本控制: Git + GitHub
包管理: pip + requirements.txt
测试框架: pytest
代码格式: black + flake8
```

### 依赖包详细说明

#### 核心依赖
```python
# requirements.txt

# 游戏引擎
renpy==8.4.3                    # 视觉小说游戏引擎

# 数据库相关
sqlalchemy==2.0.23              # ORM框架
alembic==1.13.1                 # 数据库迁移工具

# AI集成
openai==1.3.7                   # OpenAI API客户端
anthropic==0.7.8                # Anthropic Claude API
ollama-python==0.1.7            # Ollama本地LLM客户端
tiktoken==0.5.2                 # Token计算工具

# 数据处理
pandas==2.1.4                   # 数据分析库
numpy==1.24.3                   # 数值计算库
pydantic==2.5.2                 # 数据验证库

# 网络请求
requests==2.31.0                # HTTP请求库
aiohttp==3.9.1                  # 异步HTTP客户端

# 配置管理
python-dotenv==1.0.0            # 环境变量管理
pyyaml==6.0.1                   # YAML配置文件支持

# 日志和调试
loguru==0.7.2                   # 现代化日志库
rich==13.7.0                    # 美化终端输出
tqdm==4.66.1                    # 进度条库

# 开发工具
pytest==7.4.3                   # 测试框架
pytest-asyncio==0.21.1          # 异步测试支持
black==23.11.0                  # 代码格式化
flake8==6.1.0                   # 代码检查
mypy==1.7.1                     # 类型检查

# 性能监控
psutil==5.9.6                   # 系统资源监控
memory-profiler==0.61.0         # 内存使用分析
```

## 项目架构设计

### 目录结构
```
LIFE_MVP/
├── game/                        # Renpy游戏主目录
│   ├── python-packages/         # Python包目录
│   │   └── life_core/           # 核心游戏逻辑包
│   │       ├── __init__.py
│   │       ├── core/            # 核心系统
│   │       │   ├── __init__.py
│   │       │   ├── engine.py    # 游戏引擎核心
│   │       │   ├── config.py    # 配置管理
│   │       │   ├── events.py    # 事件系统
│   │       │   └── logger.py    # 日志系统
│   │       ├── data/            # 数据管理
│   │       │   ├── __init__.py
│   │       │   ├── database.py  # 数据库操作
│   │       │   ├── models.py    # 数据模型
│   │       │   ├── repository.py # 数据仓库模式
│   │       │   └── migrations/  # 数据库迁移
│   │       ├── ai/              # AI集成
│   │       │   ├── __init__.py
│   │       │   ├── llm_client.py # LLM客户端抽象
│   │       │   ├── dialogue.py   # 对话管理
│   │       │   ├── character.py  # 角色AI
│   │       │   └── prompts/      # 提示词模板
│   │       ├── world/           # 世界系统
│   │       │   ├── __init__.py
│   │       │   ├── geography.py  # 地理系统
│   │       │   ├── time_mgr.py   # 时间管理
│   │       │   ├── locations.py  # 地点管理
│   │       │   └── events.py     # 世界事件
│   │       ├── npc/             # NPC系统
│   │       │   ├── __init__.py
│   │       │   ├── manager.py    # NPC管理器
│   │       │   ├── attributes.py # 属性系统
│   │       │   ├── behavior.py   # 行为系统
│   │       │   ├── memory.py     # 记忆系统
│   │       │   └── relations.py  # 关系系统
│   │       ├── economy/         # 经济系统
│   │       │   ├── __init__.py
│   │       │   ├── currency.py   # 货币系统
│   │       │   ├── market.py     # 市场系统
│   │       │   ├── jobs.py       # 工作系统
│   │       │   └── transactions.py # 交易系统
│   │       └── utils/           # 工具模块
│   │           ├── __init__.py
│   │           ├── helpers.py    # 辅助函数
│   │           ├── validators.py # 数据验证
│   │           └── decorators.py # 装饰器
│   ├── images/                  # 图片资源
│   │   ├── ui/                  # UI图片
│   │   ├── characters/          # 角色立绘
│   │   └── backgrounds/         # 背景图片
│   ├── audio/                   # 音频资源
│   │   ├── music/               # 背景音乐
│   │   ├── sfx/                 # 音效
│   │   └── voice/               # 语音（预留）
│   ├── gui/                     # GUI资源
│   ├── tl/                      # 翻译文件
│   ├── script.rpy               # 主脚本
│   ├── options.rpy              # 游戏选项
│   ├── gui.rpy                  # GUI配置
│   ├── screens.rpy              # 界面定义
│   └── init.rpy                 # 初始化脚本
├── data/                        # 数据文件
│   ├── database/                # 数据库文件
│   │   └── life_game.db
│   ├── config/                  # 配置文件
│   │   ├── game_config.yaml
│   │   ├── ai_config.yaml
│   │   └── world_config.yaml
│   ├── saves/                   # 存档文件
│   └── logs/                    # 日志文件
├── assets/                      # 原始资源文件
│   ├── psd/                     # Photoshop文件
│   ├── audio_src/               # 音频源文件
│   └── docs/                    # 设计文档
├── tests/                       # 测试文件
│   ├── unit/                    # 单元测试
│   ├── integration/             # 集成测试
│   ├── fixtures/                # 测试数据
│   └── conftest.py              # pytest配置
├── scripts/                     # 脚本工具
│   ├── setup_dev.py             # 开发环境设置
│   ├── build.py                 # 构建脚本
│   └── deploy.py                # 部署脚本
├── docs/                        # 文档
│   ├── api/                     # API文档
│   ├── dev/                     # 开发文档
│   └── user/                    # 用户文档
├── .env.example                 # 环境变量示例
├── .gitignore                   # Git忽略文件
├── requirements.txt             # Python依赖
├── requirements-dev.txt         # 开发依赖
├── setup.py                     # 安装脚本
├── pyproject.toml               # 项目配置
└── README.md                    # 项目说明
```

## 核心模块设计

### 1. 配置管理系统

#### config.py
```python
from typing import Dict, Any, Optional
from pathlib import Path
import yaml
from pydantic import BaseModel, Field
from dotenv import load_dotenv
import os

class DatabaseConfig(BaseModel):
    """数据库配置"""
    url: str = Field(default="sqlite:///data/database/life_game.db")
    echo: bool = Field(default=False)
    pool_size: int = Field(default=5)
    max_overflow: int = Field(default=10)

class AIConfig(BaseModel):
    """AI配置"""
    provider: str = Field(default="openai")  # openai, anthropic, ollama
    api_key: Optional[str] = Field(default=None)
    base_url: Optional[str] = Field(default=None)
    model: str = Field(default="gpt-3.5-turbo")
    max_tokens: int = Field(default=1000)
    temperature: float = Field(default=0.7)
    timeout: int = Field(default=30)

class GameConfig(BaseModel):
    """游戏配置"""
    debug_mode: bool = Field(default=False)
    auto_save_interval: int = Field(default=300)  # 秒
    max_save_slots: int = Field(default=10)
    language: str = Field(default="zh_CN")

class Config:
    """配置管理器"""

    def __init__(self, config_dir: Path = Path("data/config")):
        self.config_dir = config_dir
        load_dotenv()  # 加载环境变量

        self.database = self._load_database_config()
        self.ai = self._load_ai_config()
        self.game = self._load_game_config()

    def _load_database_config(self) -> DatabaseConfig:
        """加载数据库配置"""
        config_file = self.config_dir / "database.yaml"
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            return DatabaseConfig(**data)
        return DatabaseConfig()

    def _load_ai_config(self) -> AIConfig:
        """加载AI配置"""
        config_file = self.config_dir / "ai.yaml"
        data = {}

        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)

        # 环境变量覆盖配置文件
        if os.getenv("OPENAI_API_KEY"):
            data["api_key"] = os.getenv("OPENAI_API_KEY")
        if os.getenv("AI_PROVIDER"):
            data["provider"] = os.getenv("AI_PROVIDER")

        return AIConfig(**data)

    def _load_game_config(self) -> GameConfig:
        """加载游戏配置"""
        config_file = self.config_dir / "game.yaml"
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            return GameConfig(**data)
        return GameConfig()

# 全局配置实例
config = Config()
```

### 2. 数据库模型设计

#### models.py
```python
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, ForeignKey, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Optional

Base = declarative_base()

class NPC(Base):
    """NPC模型"""
    __tablename__ = "npcs"

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    age = Column(Integer, default=25)
    gender = Column(String(10), default="未知")
    occupation = Column(String(100))

    # 基础属性
    intelligence = Column(Integer, default=50)  # 智力
    charisma = Column(Integer, default=50)      # 魅力
    stamina = Column(Integer, default=50)       # 体力
    financial_iq = Column(Integer, default=50)  # 财商
    emotional_iq = Column(Integer, default=50)  # 情商

    # 经济状况
    cash = Column(Float, default=1000.0)
    monthly_income = Column(Float, default=3000.0)
    monthly_expense = Column(Float, default=2500.0)

    # 心理状态
    happiness = Column(Integer, default=70)     # 幸福度
    stress = Column(Integer, default=30)        # 压力值
    ambition = Column(Integer, default=50)      # 野心
    risk_preference = Column(Integer, default=50)  # 风险偏好

    # 位置信息
    current_location_id = Column(Integer, ForeignKey("locations.id"))
    current_location = relationship("Location", back_populates="npcs")

    # 关系
    relationships = relationship("Relationship", foreign_keys="Relationship.npc_id")

    # 记忆
    memories = relationship("Memory", back_populates="npc")

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Location(Base):
    """地点模型"""
    __tablename__ = "locations"

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    type = Column(String(50))  # 住宅、商业、工业、公共设施
    level = Column(Integer, default=1)  # 1=国家, 2=市, 3=区
    parent_id = Column(Integer, ForeignKey("locations.id"))

    # 空间信息
    space_capacity = Column(Integer, default=100)
    space_used = Column(Integer, default=0)

    # 关系
    parent = relationship("Location", remote_side=[id])
    children = relationship("Location")
    npcs = relationship("NPC", back_populates="current_location")

    created_at = Column(DateTime, default=datetime.utcnow)

class Relationship(Base):
    """关系模型"""
    __tablename__ = "relationships"

    id = Column(Integer, primary_key=True)
    npc_id = Column(Integer, ForeignKey("npcs.id"), nullable=False)
    target_npc_id = Column(Integer, ForeignKey("npcs.id"), nullable=False)

    relationship_type = Column(String(50))  # 家庭、朋友、同事、敌人
    strength = Column(Integer, default=50)  # 关系强度 0-100

    npc = relationship("NPC", foreign_keys=[npc_id])
    target_npc = relationship("NPC", foreign_keys=[target_npc_id])

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Memory(Base):
    """记忆模型"""
    __tablename__ = "memories"

    id = Column(Integer, primary_key=True)
    npc_id = Column(Integer, ForeignKey("npcs.id"), nullable=False)

    memory_type = Column(String(50))  # 短期、长期、情景、程序性
    content = Column(Text, nullable=False)
    importance = Column(Integer, default=50)  # 重要性 0-100
    emotional_impact = Column(Integer, default=0)  # 情感影响 -100到100

    # 关联信息
    related_npc_id = Column(Integer, ForeignKey("npcs.id"))
    related_location_id = Column(Integer, ForeignKey("locations.id"))

    npc = relationship("NPC", foreign_keys=[npc_id], back_populates="memories")
    related_npc = relationship("NPC", foreign_keys=[related_npc_id])
    related_location = relationship("Location", foreign_keys=[related_location_id])

    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)  # 过期时间，用于短期记忆

class GameState(Base):
    """游戏状态模型"""
    __tablename__ = "game_states"

    id = Column(Integer, primary_key=True)
    current_day = Column(Integer, default=1)
    current_hour = Column(Integer, default=8)  # 0-23
    game_speed = Column(Integer, default=1)    # 游戏速度倍数

    # 玩家信息
    player_npc_id = Column(Integer, ForeignKey("npcs.id"))
    player_npc = relationship("NPC")

    # 游戏统计
    total_play_time = Column(Integer, default=0)  # 总游戏时间（分钟）

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 3. AI集成层设计

#### llm_client.py
```python
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
import asyncio
import openai
import anthropic
import requests
from loguru import logger
from life_core.core.config import config

class LLMClient(ABC):
    """LLM客户端抽象基类"""

    @abstractmethod
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> str:
        """生成回复"""
        pass

    @abstractmethod
    def count_tokens(self, text: str) -> int:
        """计算token数量"""
        pass

class OpenAIClient(LLMClient):
    """OpenAI客户端"""

    def __init__(self):
        self.client = openai.AsyncOpenAI(
            api_key=config.ai.api_key,
            base_url=config.ai.base_url
        )
        self.model = config.ai.model

    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> str:
        """生成回复"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=kwargs.get('max_tokens', config.ai.max_tokens),
                temperature=kwargs.get('temperature', config.ai.temperature),
                timeout=config.ai.timeout
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            return "抱歉，我现在无法回应。"

    def count_tokens(self, text: str) -> int:
        """计算token数量"""
        try:
            import tiktoken
            encoding = tiktoken.encoding_for_model(self.model)
            return len(encoding.encode(text))
        except:
            # 简单估算：中文约1.5字符/token，英文约4字符/token
            return len(text) // 2

class AnthropicClient(LLMClient):
    """Anthropic Claude客户端"""

    def __init__(self):
        self.client = anthropic.AsyncAnthropic(
            api_key=config.ai.api_key
        )
        self.model = config.ai.model

    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> str:
        """生成回复"""
        try:
            # 转换消息格式
            system_message = ""
            user_messages = []

            for msg in messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    user_messages.append(msg)

            response = await self.client.messages.create(
                model=self.model,
                system=system_message,
                messages=user_messages,
                max_tokens=kwargs.get('max_tokens', config.ai.max_tokens),
                temperature=kwargs.get('temperature', config.ai.temperature)
            )
            return response.content[0].text
        except Exception as e:
            logger.error(f"Anthropic API调用失败: {e}")
            return "抱歉，我现在无法回应。"

    def count_tokens(self, text: str) -> int:
        """计算token数量"""
        # Claude的token计算较复杂，这里简单估算
        return len(text) // 2

class OllamaClient(LLMClient):
    """Ollama本地客户端"""

    def __init__(self):
        self.base_url = config.ai.base_url or "http://localhost:11434"
        self.model = config.ai.model

    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> str:
        """生成回复"""
        try:
            # 构建prompt
            prompt = self._build_prompt(messages)

            data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": kwargs.get('temperature', config.ai.temperature),
                    "num_predict": kwargs.get('max_tokens', config.ai.max_tokens)
                }
            }

            response = requests.post(
                f"{self.base_url}/api/generate",
                json=data,
                timeout=config.ai.timeout
            )
            response.raise_for_status()

            return response.json()["response"]
        except Exception as e:
            logger.error(f"Ollama API调用失败: {e}")
            return "抱歉，我现在无法回应。"

    def _build_prompt(self, messages: List[Dict[str, str]]) -> str:
        """构建prompt"""
        prompt_parts = []
        for msg in messages:
            role = msg["role"]
            content = msg["content"]
            if role == "system":
                prompt_parts.append(f"系统: {content}")
            elif role == "user":
                prompt_parts.append(f"用户: {content}")
            elif role == "assistant":
                prompt_parts.append(f"助手: {content}")

        return "\n".join(prompt_parts) + "\n助手: "

    def count_tokens(self, text: str) -> int:
        """计算token数量"""
        return len(text) // 2

class LLMClientFactory:
    """LLM客户端工厂"""

    @staticmethod
    def create_client() -> LLMClient:
        """根据配置创建客户端"""
        provider = config.ai.provider.lower()

        if provider == "openai":
            return OpenAIClient()
        elif provider == "anthropic":
            return AnthropicClient()
        elif provider == "ollama":
            return OllamaClient()
        else:
            logger.warning(f"未知的AI提供商: {provider}，使用OpenAI作为默认")
            return OpenAIClient()

# 全局LLM客户端实例
llm_client = LLMClientFactory.create_client()
```

## MVP开发流程

### 开发环境搭建

#### 1. 环境准备
```bash
# 1. 安装Python 3.11+
# 2. 安装Renpy 8.4.3
# 3. 创建项目目录
mkdir LIFE_MVP
cd LIFE_MVP

# 4. 创建虚拟环境
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 5. 安装依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

#### 2. 配置文件设置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，添加API密钥
# OPENAI_API_KEY=your_openai_api_key
# AI_PROVIDER=openai
# DEBUG_MODE=true
```

#### 3. 数据库初始化
```python
# scripts/setup_dev.py
from life_core.data.database import init_database
from life_core.data.models import Base
from life_core.core.config import config

def setup_development_environment():
    """设置开发环境"""
    print("初始化数据库...")
    init_database()

    print("创建示例数据...")
    create_sample_data()

    print("开发环境设置完成！")

def create_sample_data():
    """创建示例数据"""
    from life_core.data.repository import NPCRepository, LocationRepository

    # 创建示例地点
    location_repo = LocationRepository()
    country = location_repo.create_location("中华联邦", "国家", 1)
    city = location_repo.create_location("新海市", "城市", 2, country.id)
    district = location_repo.create_location("科技区", "区域", 3, city.id)

    # 创建示例NPC
    npc_repo = NPCRepository()
    npc1 = npc_repo.create_npc(
        name="李小明",
        age=28,
        gender="男",
        occupation="软件工程师",
        location_id=district.id
    )

    npc2 = npc_repo.create_npc(
        name="王小红",
        age=25,
        gender="女",
        occupation="设计师",
        location_id=district.id
    )

    print(f"创建了示例NPC: {npc1.name}, {npc2.name}")

if __name__ == "__main__":
    setup_development_environment()
```

### 开发阶段规划

#### 阶段1：基础框架（第1-2周）
```python
# 任务清单
Phase1_Tasks = [
    "✅ 项目结构搭建",
    "✅ 配置管理系统",
    "✅ 数据库模型设计",
    "✅ 基础工具函数",
    "⏳ 日志系统集成",
    "⏳ 单元测试框架",
    "⏳ CI/CD流程设置"
]
```

#### 阶段2：核心系统（第3-5周）
```python
Phase2_Tasks = [
    "⏳ AI对话系统集成",
    "⏳ NPC管理系统",
    "⏳ 世界时间系统",
    "⏳ 基础经济系统",
    "⏳ 关系管理系统",
    "⏳ 记忆系统实现",
    "⏳ 事件系统框架"
]
```

#### 阶段3：游戏逻辑（第6-7周）
```python
Phase3_Tasks = [
    "⏳ Renpy界面集成",
    "⏳ 对话界面实现",
    "⏳ 游戏循环逻辑",
    "⏳ 存档系统",
    "⏳ 设置界面",
    "⏳ 调试工具",
    "⏳ 性能优化"
]
```

#### 阶段4：测试发布（第8周）
```python
Phase4_Tasks = [
    "⏳ 全面测试",
    "⏳ 用户体验优化",
    "⏳ 文档编写",
    "⏳ 打包发布",
    "⏳ 部署指南",
    "⏳ 用户反馈收集"
]
```

### 代码规范

#### 1. Python代码规范
```python
# 使用black进行代码格式化
black --line-length 88 life_core/

# 使用flake8进行代码检查
flake8 life_core/ --max-line-length=88

# 使用mypy进行类型检查
mypy life_core/ --ignore-missing-imports
```

#### 2. 提交规范
```bash
# 提交消息格式
# <type>(<scope>): <subject>
#
# <body>
#
# <footer>

# 示例
git commit -m "feat(ai): 添加OpenAI客户端支持

- 实现OpenAI API调用封装
- 添加token计算功能
- 支持异步调用和错误处理

Closes #123"
```

#### 3. 分支管理
```bash
# 主分支
main          # 生产环境代码
develop       # 开发环境代码

# 功能分支
feature/ai-integration     # AI集成功能
feature/npc-system        # NPC系统
feature/ui-design         # UI设计

# 修复分支
hotfix/critical-bug       # 紧急修复
```

### 测试策略

#### 1. 单元测试
```python
# tests/unit/test_npc_manager.py
import pytest
from life_core.npc.manager import NPCManager
from life_core.data.models import NPC

class TestNPCManager:

    def setup_method(self):
        """测试前准备"""
        self.npc_manager = NPCManager()

    def test_create_npc(self):
        """测试创建NPC"""
        npc = self.npc_manager.create_npc(
            name="测试NPC",
            age=25,
            gender="男"
        )

        assert npc.name == "测试NPC"
        assert npc.age == 25
        assert npc.gender == "男"

    def test_npc_attributes(self):
        """测试NPC属性"""
        npc = self.npc_manager.create_npc("测试NPC")

        # 测试默认属性值
        assert 0 <= npc.intelligence <= 100
        assert 0 <= npc.charisma <= 100
        assert npc.cash >= 0

    @pytest.mark.asyncio
    async def test_npc_dialogue(self):
        """测试NPC对话"""
        npc = self.npc_manager.create_npc("测试NPC")

        response = await self.npc_manager.generate_dialogue(
            npc, "你好"
        )

        assert isinstance(response, str)
        assert len(response) > 0
```

#### 2. 集成测试
```python
# tests/integration/test_game_flow.py
import pytest
from life_core.core.engine import GameEngine

class TestGameFlow:

    @pytest.mark.asyncio
    async def test_complete_game_cycle(self):
        """测试完整游戏循环"""
        engine = GameEngine()

        # 初始化游戏
        await engine.initialize()

        # 创建玩家角色
        player = await engine.create_player("测试玩家")

        # 执行一个游戏回合
        await engine.advance_time(1)

        # 验证游戏状态
        game_state = engine.get_game_state()
        assert game_state.current_day == 2
        assert player.id == game_state.player_npc_id
```

### 性能监控

#### 1. 性能指标
```python
# life_core/utils/performance.py
import time
import psutil
from functools import wraps
from loguru import logger

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024

        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024

            execution_time = end_time - start_time
            memory_usage = end_memory - start_memory

            logger.info(
                f"函数 {func.__name__} 执行时间: {execution_time:.3f}s, "
                f"内存变化: {memory_usage:.2f}MB"
            )

    return wrapper

class PerformanceTracker:
    """性能追踪器"""

    def __init__(self):
        self.metrics = {}

    def track_ai_call(self, duration: float, tokens: int):
        """追踪AI调用性能"""
        if "ai_calls" not in self.metrics:
            self.metrics["ai_calls"] = []

        self.metrics["ai_calls"].append({
            "duration": duration,
            "tokens": tokens,
            "timestamp": time.time()
        })

    def get_average_ai_response_time(self) -> float:
        """获取AI平均响应时间"""
        if "ai_calls" not in self.metrics:
            return 0.0

        calls = self.metrics["ai_calls"]
        if not calls:
            return 0.0

        total_time = sum(call["duration"] for call in calls)
        return total_time / len(calls)
```

### 部署指南

#### 1. 本地开发部署
```bash
# 1. 启动开发服务器
python scripts/setup_dev.py

# 2. 运行游戏
cd game
renpy .

# 3. 运行测试
pytest tests/ -v

# 4. 生成覆盖率报告
pytest --cov=life_core tests/
```

#### 2. 生产环境打包
```python
# scripts/build.py
import os
import shutil
from pathlib import Path

def build_release():
    """构建发布版本"""
    print("开始构建发布版本...")

    # 清理构建目录
    build_dir = Path("build")
    if build_dir.exists():
        shutil.rmtree(build_dir)
    build_dir.mkdir()

    # 复制游戏文件
    shutil.copytree("game", build_dir / "game")
    shutil.copytree("data", build_dir / "data")

    # 复制必要文件
    shutil.copy("requirements.txt", build_dir)
    shutil.copy("README.md", build_dir)

    # 创建启动脚本
    create_launcher_script(build_dir)

    print("构建完成！输出目录: build/")

def create_launcher_script(build_dir: Path):
    """创建启动脚本"""
    launcher_content = """#!/bin/bash
# LIFE Game Launcher

echo "启动LIFE游戏..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.11+"
    exit 1
fi

# 检查Renpy
if ! command -v renpy &> /dev/null; then
    echo "错误: 未找到Renpy，请先安装Renpy 8.4+"
    exit 1
fi

# 启动游戏
cd game
renpy .
"""

    launcher_path = build_dir / "start_game.sh"
    with open(launcher_path, 'w') as f:
        f.write(launcher_content)

    # 设置执行权限
    os.chmod(launcher_path, 0o755)

if __name__ == "__main__":
    build_release()
```

#### 3. Docker部署（可选）
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 安装Renpy
RUN wget https://www.renpy.org/dl/8.4.3/renpy-8.4.3-sdk.tar.bz2 \
    && tar -xjf renpy-8.4.3-sdk.tar.bz2 \
    && mv renpy-8.4.3-sdk /opt/renpy \
    && ln -s /opt/renpy/renpy.sh /usr/local/bin/renpy

# 复制项目文件
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# 设置环境变量
ENV PYTHONPATH=/app

# 暴露端口（如果需要web界面）
EXPOSE 8000

# 启动命令
CMD ["python", "scripts/setup_dev.py"]
```

---

*MVP技术栈实现指南完成，为开发团队提供详细的技术实现路线图*