#!/usr/bin/env python3
"""
Ren'Py语法检查脚本
检查常见的Ren'Py语法错误
"""

import os
import re
from pathlib import Path

def check_renpy_syntax(file_path):
    """检查Ren'Py文件的语法"""
    errors = []
    warnings = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        return [f"无法读取文件: {e}"], []
    
    for i, line in enumerate(lines, 1):
        line_stripped = line.strip()
        
        # 检查常见错误
        
        # 1. 检查 'at' 变换的使用
        if re.search(r'use\s+\w+\s*\n\s*at\s+', line + (lines[i] if i < len(lines) else '')):
            errors.append(f"行 {i}: 'at' 变换不能直接跟在 'use' 语句后面")
        
        # 2. 检查未闭合的括号
        open_parens = line_stripped.count('(') - line_stripped.count(')')
        open_brackets = line_stripped.count('[') - line_stripped.count(']')
        open_braces = line_stripped.count('{') - line_stripped.count('}')
        
        if open_parens > 0 or open_brackets > 0 or open_braces > 0:
            if not line_stripped.endswith('\\'):
                warnings.append(f"行 {i}: 可能存在未闭合的括号")
        
        # 3. 检查缩进问题
        if line.startswith(' ') and not line.startswith('    '):
            if len(line) - len(line.lstrip()) not in [0, 4, 8, 12, 16, 20]:
                warnings.append(f"行 {i}: 缩进可能不正确（应使用4的倍数空格）")
        
        # 4. 检查常见的拼写错误
        common_mistakes = {
            'colour': 'color',
            'centre': 'center',
            'grey': 'gray'
        }
        
        for mistake, correction in common_mistakes.items():
            if mistake in line_stripped:
                warnings.append(f"行 {i}: 建议使用 '{correction}' 而不是 '{mistake}'")
        
        # 5. 检查screen语句的结构
        if line_stripped.startswith('screen '):
            if not line_stripped.endswith(':'):
                errors.append(f"行 {i}: screen 语句应以冒号结尾")
        
        # 6. 检查transform语句的结构
        if line_stripped.startswith('transform '):
            if not line_stripped.endswith(':'):
                errors.append(f"行 {i}: transform 语句应以冒号结尾")
    
    return errors, warnings

def main():
    """主函数"""
    print("Ren'Py语法检查器")
    print("=" * 50)
    
    # 检查game目录下的.rpy文件
    game_dir = Path("game")
    if not game_dir.exists():
        print("错误: 找不到game目录")
        return 1
    
    rpy_files = list(game_dir.glob("*.rpy"))
    if not rpy_files:
        print("警告: game目录下没有找到.rpy文件")
        return 1
    
    total_errors = 0
    total_warnings = 0
    
    for file_path in rpy_files:
        print(f"\n检查文件: {file_path}")
        print("-" * 30)
        
        errors, warnings = check_renpy_syntax(file_path)
        
        if errors:
            print("❌ 错误:")
            for error in errors:
                print(f"  {error}")
            total_errors += len(errors)
        
        if warnings:
            print("⚠️  警告:")
            for warning in warnings:
                print(f"  {warning}")
            total_warnings += len(warnings)
        
        if not errors and not warnings:
            print("✅ 语法检查通过")
    
    print(f"\n" + "=" * 50)
    print(f"检查完成: {len(rpy_files)} 个文件")
    print(f"总错误数: {total_errors}")
    print(f"总警告数: {total_warnings}")
    
    if total_errors > 0:
        print("\n❌ 发现语法错误，请修复后重试")
        return 1
    elif total_warnings > 0:
        print("\n⚠️  发现一些警告，建议检查")
        return 0
    else:
        print("\n✅ 所有文件语法检查通过！")
        return 0

if __name__ == "__main__":
    import sys
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n用户取消操作")
        sys.exit(0)
    except Exception as e:
        print(f"发生错误: {e}")
        sys.exit(1)
