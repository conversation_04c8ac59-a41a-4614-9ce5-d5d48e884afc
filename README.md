# LIFE游戏 - UI界面系统

基于参考图片设计和实现的LIFE游戏用户界面系统，使用Ren'Py引擎开发。

## 项目概述

这是一个模拟经营类游戏的UI界面实现，包含以下主要组件：

### 界面组件

1. **顶部状态栏**
   - 角色基本信息（职业、日期、角色类型）
   - 资源显示（体力、金钱、各种道具）
   - 实时状态更新

2. **左侧角色面板**
   - 主角色头像显示
   - 角色状态信息（压力值、时间信息）
   - 快捷功能按钮（日记本、自习室）

3. **右侧角色列表**
   - NPC角色头像列表
   - 角色名称标签
   - 点击交互功能

4. **底部功能栏**
   - 8个主要功能按钮（刷题、学习、问候、获奖、外出、住所、网购、睡觉）
   - 圆形图标设计
   - 悬停动画效果

5. **主游戏区域**
   - 3D场景显示区域（当前为占位）
   - 交互按钮
   - 对话和状态显示

## 技术实现

### 文件结构

```
game/
├── screens.rpy          # 主界面定义
├── life_ui_styles.rpy   # UI样式系统
├── script.rpy           # 游戏逻辑和交互处理
├── gui.rpy              # GUI配置
└── options.rpy          # 游戏选项
```

### 样式系统

- **颜色主题**: 统一的蓝色调色板
- **响应式设计**: 支持不同屏幕尺寸
- **动画效果**: 按钮悬停、面板淡入等
- **组件化**: 可复用的UI组件

### 交互系统

- **屏幕调用**: 使用`call screen`进行界面交互
- **返回值处理**: 支持多种交互结果
- **状态管理**: 游戏状态的保存和更新

## 如何运行

### 方法1: 使用Ren'Py SDK

1. 下载并安装 [Ren'Py SDK](https://www.renpy.org/latest.html)
2. 在Ren'Py启动器中选择"打开项目"
3. 选择本项目文件夹
4. 点击"启动项目"

### 方法2: 预览界面

打开 `ui_preview.html` 文件可以预览界面设计效果（静态版本）

## 功能特性

### 已实现功能

- ✅ 完整的UI布局系统
- ✅ 统一的样式主题
- ✅ 基本的交互逻辑
- ✅ 响应式设计支持
- ✅ 动画和转场效果

### 待实现功能

- ⏳ 3D场景集成
- ⏳ 角色AI对话系统
- ⏳ 数据持久化
- ⏳ 音效和背景音乐
- ⏳ 更多游戏功能

## 设计说明

界面设计参考了现代模拟经营游戏的UI风格：

- **色彩搭配**: 以天蓝色为主色调，营造清新舒适的视觉体验
- **布局结构**: 采用经典的四边布局，信息层次清晰
- **交互设计**: 按钮具有明显的视觉反馈，提升用户体验
- **信息密度**: 合理安排信息显示，避免界面过于拥挤

## 开发说明

### 样式定制

所有UI样式都定义在 `life_ui_styles.rpy` 中，包括：
- 颜色主题定义
- 组件样式规范
- 动画效果配置
- 响应式适配

### 添加新功能

1. 在 `screens.rpy` 中定义新的界面组件
2. 在 `life_ui_styles.rpy` 中添加对应样式
3. 在 `script.rpy` 中实现交互逻辑

### 调试技巧

- 使用Ren'Py的开发者控制台进行调试
- 检查 `log.txt` 文件查看错误信息
- 使用 `renpy.restart_interaction()` 刷新界面

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

本项目采用MIT许可证。
