<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LIFE游戏UI预览</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #87CEEB, #E0F6FF);
            height: 100vh;
            overflow: hidden;
        }
        
        .main-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        /* 顶部状态栏 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: #87CEEB;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 30px;
            color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .character-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .avatar-small {
            width: 50px;
            height: 50px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 10px;
        }
        
        .resources {
            display: flex;
            gap: 25px;
            align-items: center;
        }
        
        .resource-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        /* 左侧角色面板 */
        .character-panel {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 180px;
            height: 500px;
            background: #E8F4FD;
            border-radius: 0 15px 15px 0;
            padding: 20px;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .avatar-large {
            width: 140px;
            height: 140px;
            background: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: #333;
        }
        
        .character-status {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .quick-button {
            width: 120px;
            height: 35px;
            background: #90EE90;
            border: none;
            border-radius: 5px;
            margin: 8px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .quick-button:hover {
            transform: scale(1.05);
        }
        
        /* 右侧角色列表 */
        .character-list {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 120px;
            height: 600px;
            background: #E8F4FD;
            border-radius: 15px 0 0 15px;
            padding: 20px;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }
        
        .character-item {
            margin-bottom: 15px;
            text-align: center;
        }
        
        .character-avatar {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            margin-bottom: 5px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .character-avatar:hover {
            transform: scale(1.1);
        }
        
        .character-label {
            background: #87CEEB;
            color: white;
            padding: 5px;
            border-radius: 5px;
            font-size: 10px;
        }
        
        /* 底部功能栏 */
        .function-bar {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 1000px;
            height: 120px;
            background: #F0F8FF;
            border-radius: 15px 15px 0 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 25px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
        
        .function-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }
        
        .function-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .function-icon:hover {
            transform: scale(1.1);
        }
        
        .function-label {
            font-size: 12px;
            color: #333;
        }
        
        /* 主游戏区域 */
        .game-area {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 800px;
            height: 600px;
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .game-title {
            font-size: 32px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .game-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
        }
        
        .game-buttons {
            display: flex;
            gap: 20px;
        }
        
        .game-button {
            padding: 10px 20px;
            background: #87CEEB;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .game-button:hover {
            background: #5F9EA0;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 顶部状态栏 -->
        <div class="status-bar">
            <div class="character-info">
                <div class="avatar-small">头像</div>
                <div>
                    <div style="font-size: 16px;">高级程序员</div>
                    <div style="font-size: 12px;">09年03月上旬</div>
                    <div style="font-size: 12px;">日常角</div>
                </div>
            </div>
            
            <div class="resources">
                <div class="resource-icon" style="background: #FF6B6B;">🎁</div>
                <span>10</span>
                <div class="resource-icon" style="background: #FFD93D;">🛍️</div>
                <span>12</span>
                <div class="resource-icon" style="background: #96CEB4;">🏠</div>
                <div class="resource-icon" style="background: #FFD700;">🏆</div>
                <span style="margin-left: 20px;">💪 406</span>
                <span>💰 5,262</span>
            </div>
        </div>
        
        <!-- 左侧角色面板 -->
        <div class="character-panel">
            <div class="avatar-large">
                <div>角色<br>头像</div>
            </div>
            <div class="character-status">
                <div style="color: #FF6B6B;">压力: 96/461</div>
                <div style="color: #666; font-size: 10px; margin-top: 5px;">Last Friday</div>
                <div style="color: #666; font-size: 10px;">11:44...</div>
            </div>
            <button class="quick-button">📖 日记本</button>
            <button class="quick-button">📚 自习室</button>
        </div>
        
        <!-- 右侧角色列表 -->
        <div class="character-list">
            <div class="character-item">
                <div class="character-avatar" style="background: #FF6B6B;">头像</div>
                <div class="character-label">医生</div>
            </div>
            <div class="character-item">
                <div class="character-avatar" style="background: #4ECDC4;">头像</div>
                <div class="character-label">男友</div>
            </div>
            <div class="character-item">
                <div class="character-avatar" style="background: #45B7D1;">头像</div>
                <div class="character-label">同事</div>
            </div>
            <div class="character-item">
                <div class="character-avatar" style="background: #96CEB4;">头像</div>
                <div class="character-label">朋友</div>
            </div>
        </div>
        
        <!-- 底部功能栏 -->
        <div class="function-bar">
            <div class="function-button">
                <div class="function-icon" style="background: #FF6B6B;">📝</div>
                <div class="function-label">刷题</div>
            </div>
            <div class="function-button">
                <div class="function-icon" style="background: #4ECDC4;">📚</div>
                <div class="function-label">学习</div>
            </div>
            <div class="function-button">
                <div class="function-icon" style="background: #45B7D1;">👋</div>
                <div class="function-label">问候</div>
            </div>
            <div class="function-button">
                <div class="function-icon" style="background: #FFD93D;">🏆</div>
                <div class="function-label">获奖</div>
            </div>
            <div class="function-button">
                <div class="function-icon" style="background: #96CEB4;">🚶</div>
                <div class="function-label">外出</div>
            </div>
            <div class="function-button">
                <div class="function-icon" style="background: #DDA0DD;">🏠</div>
                <div class="function-label">住所</div>
            </div>
            <div class="function-button">
                <div class="function-icon" style="background: #F4A460;">🛒</div>
                <div class="function-label">网购</div>
            </div>
            <div class="function-button">
                <div class="function-icon" style="background: #87CEEB;">😴</div>
                <div class="function-label">睡觉</div>
            </div>
        </div>
        
        <!-- 主游戏区域 -->
        <div class="game-area">
            <div class="game-title">LIFE 游戏主场景</div>
            <div class="game-subtitle">这里将显示3D场景和角色互动</div>
            <div class="game-buttons">
                <button class="game-button">开始对话</button>
                <button class="game-button">查看状态</button>
                <button class="game-button">返回菜单</button>
            </div>
        </div>
    </div>
</body>
</html>
