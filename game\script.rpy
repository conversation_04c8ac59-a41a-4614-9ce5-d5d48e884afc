﻿# 游戏的脚本可置于此文件中。

# 声明此游戏使用的角色。颜色参数可使角色姓名着色。

define e = Character("艾琳")


# 游戏在此开始。

label start:
    # 显示LIFE游戏主界面
    $ result = renpy.call_screen("life_main_ui")

    # 处理UI交互结果
    if result == "dialogue":
        jump dialogue_scene
    elif result == "status":
        jump status_scene
    elif result == "menu":
        jump main_menu_return
    elif result == "diary":
        jump diary_scene
    elif result == "study_room":
        jump study_room_scene
    elif isinstance(result, tuple):
        if result[0] == "character":
            $ selected_character = result[1]
            jump character_interaction
        elif result[0] == "action":
            $ selected_action = result[1]
            jump action_scene

    # 默认继续游戏
    jump game_loop

# 游戏主循环
label game_loop:
    $ result = renpy.call_screen("life_main_ui")

    # 处理交互结果
    if result == "dialogue":
        jump dialogue_scene
    elif result == "status":
        jump status_scene
    elif result == "menu":
        jump main_menu_return
    elif result == "diary":
        jump diary_scene
    elif result == "study_room":
        jump study_room_scene
    elif isinstance(result, tuple):
        if result[0] == "character":
            $ selected_character = result[1]
            jump character_interaction
        elif result[0] == "action":
            $ selected_action = result[1]
            jump action_scene

    jump game_loop

# 对话场景
label dialogue_scene:
    scene bg room
    show eileen happy

    e "欢迎来到LIFE游戏！"
    e "这是一个基于AI的生活模拟游戏，您可以与各种角色互动。"

    jump game_loop

# 状态场景
label status_scene:
    "当前状态："
    "压力: 96/461"
    "金钱: 5,262"
    "体力: 406"

    jump game_loop

# 日记场景
label diary_scene:
    "打开了日记本..."
    "今天是充实的一天。"

    jump game_loop

# 自习室场景
label study_room_scene:
    "进入了自习室..."
    "这里很安静，适合学习。"

    jump game_loop

# 角色交互场景
label character_interaction:
    "你选择与[selected_character]互动。"
    "开始对话..."

    jump game_loop

# 行动场景
label action_scene:
    "你选择了[selected_action]。"
    "执行中..."

    jump game_loop

# 返回主菜单
label main_menu_return:
    return
