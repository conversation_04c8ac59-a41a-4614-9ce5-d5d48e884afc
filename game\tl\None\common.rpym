﻿
translate None strings:

    # renpy/common/000statements.rpy:28
    old "Click to play the video."
    new "Click to play the video."

    # renpy/common/00accessibility.rpy:28
    old "Self-voicing disabled."
    new "机器朗读已禁用。"

    # renpy/common/00accessibility.rpy:29
    old "Clipboard voicing enabled. "
    new "剪贴板朗读已启用。"

    # renpy/common/00accessibility.rpy:30
    old "Self-voicing enabled. "
    new "机器朗读已启用。"

    # renpy/common/00accessibility.rpy:32
    old "bar"
    new "栏"

    # renpy/common/00accessibility.rpy:33
    old "selected"
    new "选定"

    # renpy/common/00accessibility.rpy:34
    old "viewport"
    new "视口"

    # renpy/common/00accessibility.rpy:35
    old "horizontal scroll"
    new "水平滚动"

    # renpy/common/00accessibility.rpy:36
    old "vertical scroll"
    new "垂直滚动"

    # renpy/common/00accessibility.rpy:37
    old "activate"
    new "激活"

    # renpy/common/00accessibility.rpy:38
    old "deactivate"
    new "停用"

    # renpy/common/00accessibility.rpy:39
    old "increase"
    new "增加"

    # renpy/common/00accessibility.rpy:40
    old "decrease"
    new "减少"

    # renpy/common/00accessibility.rpy:134
    old "Self-Voicing"
    new "机器朗读"

    # renpy/common/00accessibility.rpy:137
    old "Self-voicing support is limited when using a touch screen."
    new "Self-voicing support is limited when using a touch screen."

    # renpy/common/00accessibility.rpy:139
    old "Off"
    new "关闭"

    # renpy/common/00accessibility.rpy:143
    old "Text-to-speech"
    new "语音合成"

    # renpy/common/00accessibility.rpy:147
    old "Clipboard"
    new "剪贴板"

    # renpy/common/00accessibility.rpy:151
    old "Debug"
    new "调试"

    # renpy/common/00accessibility.rpy:155
    old "Voice Volume"
    new "语音音量"

    # renpy/common/00accessibility.rpy:163
    old "Reset"
    new "重置"

    # renpy/common/00accessibility.rpy:167
    old "Self-Voicing Volume Drop"
    new "机器朗读音量降低"

    # renpy/common/00accessibility.rpy:180
    old "Mono Audio"
    new "Mono Audio"

    # renpy/common/00accessibility.rpy:182
    old "Enable"
    new "启用"

    # renpy/common/00accessibility.rpy:186
    old "Disable"
    new "禁用"

    # renpy/common/00accessibility.rpy:198
    old "Font Override"
    new "字体覆盖"

    # renpy/common/00accessibility.rpy:200
    old "Default"
    new "默认"

    # renpy/common/00accessibility.rpy:204
    old "DejaVu Sans"
    new "DejaVu Sans"

    # renpy/common/00accessibility.rpy:208
    old "Opendyslexic"
    new "Opendyslexic"

    # renpy/common/00accessibility.rpy:212
    old "High Contrast Text"
    new "高对比度文本"

    # renpy/common/00accessibility.rpy:224
    old "Text Size Scaling"
    new "字号缩放"

    # renpy/common/00accessibility.rpy:235
    old "Line Spacing Scaling"
    new "行距缩放"

    # renpy/common/00accessibility.rpy:246
    old "Kerning"
    new "Kerning"

    # renpy/common/00accessibility.rpy:267
    old "Accessibility Menu. Use up and down arrows to navigate, and enter to activate buttons and bars."
    new "辅助功能菜单。使用向上和向下箭头进行导航，并使用 Enter 激活按钮和栏"

    # renpy/common/00accessibility.rpy:288
    old "Self-Voicing and Audio"
    new "Self-Voicing and Audio"

    # renpy/common/00accessibility.rpy:292
    old "Text"
    new "Text"

    # renpy/common/00accessibility.rpy:296
    old "Return"
    new "返回"

    # renpy/common/00accessibility.rpy:306
    old "The options on this menu are intended to improve accessibility. They may not work with all games, and some combinations of options may render the game unplayable. This is not an issue with the game or engine. For the best results when changing fonts, try to keep the text size the same as it originally was."
    new "此菜单上的选项旨在改善无障碍文本可读性。这些选项可能不适用于所有游戏，某些选项的组合甚至会导致游戏无法游玩。这不是游戏或引擎的问题。为了在更改字体时获得最佳效果，请尽量保持文字大小与原本相同。"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Monday"
    new "{#weekday}星期一"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Tuesday"
    new "{#weekday}星期二"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Wednesday"
    new "{#weekday}星期三"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Thursday"
    new "{#weekday}星期四"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Friday"
    new "{#weekday}星期五"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Saturday"
    new "{#weekday}星期六"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Sunday"
    new "{#weekday}星期日"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Mon"
    new "{#weekday_short}周一"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Tue"
    new "{#weekday_short}周二"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Wed"
    new "{#weekday_short}周三"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Thu"
    new "{#weekday_short}周四"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Fri"
    new "{#weekday_short}周五"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Sat"
    new "{#weekday_short}周六"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Sun"
    new "{#weekday_short}周日"

    # renpy/common/00action_file.rpy:47
    old "{#month}January"
    new "{#month}1月"

    # renpy/common/00action_file.rpy:47
    old "{#month}February"
    new "{#month}2月"

    # renpy/common/00action_file.rpy:47
    old "{#month}March"
    new "{#month}3月"

    # renpy/common/00action_file.rpy:47
    old "{#month}April"
    new "{#month}4月"

    # renpy/common/00action_file.rpy:47
    old "{#month}May"
    new "{#month}5月"

    # renpy/common/00action_file.rpy:47
    old "{#month}June"
    new "{#month}6月"

    # renpy/common/00action_file.rpy:47
    old "{#month}July"
    new "{#month}7月"

    # renpy/common/00action_file.rpy:47
    old "{#month}August"
    new "{#month}8月"

    # renpy/common/00action_file.rpy:47
    old "{#month}September"
    new "{#month}9月"

    # renpy/common/00action_file.rpy:47
    old "{#month}October"
    new "{#month}10月"

    # renpy/common/00action_file.rpy:47
    old "{#month}November"
    new "{#month}11月"

    # renpy/common/00action_file.rpy:47
    old "{#month}December"
    new "{#month}12月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jan"
    new "{#month_short}1月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Feb"
    new "{#month_short}2月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Mar"
    new "{#month_short}3月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Apr"
    new "{#month_short}4月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}May"
    new "{#month_short}5月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jun"
    new "{#month_short}6月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jul"
    new "{#month_short}7月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Aug"
    new "{#month_short}8月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Sep"
    new "{#month_short}9月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Oct"
    new "{#month_short}10月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Nov"
    new "{#month_short}11月"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Dec"
    new "{#month_short}12月"

    # renpy/common/00action_file.rpy:258
    old "%b %d, %H:%M"
    new "%m-%d %H:%M"

    # renpy/common/00action_file.rpy:395
    old "Save slot %s: [text]"
    new "保存存档 %s：[text]"

    # renpy/common/00action_file.rpy:481
    old "Load slot %s: [text]"
    new "读取存档 %s：[text]"

    # renpy/common/00action_file.rpy:534
    old "Delete slot [text]"
    new "删除存档 [text]"

    # renpy/common/00action_file.rpy:613
    old "File page auto"
    new "自动存档"

    # renpy/common/00action_file.rpy:615
    old "File page quick"
    new "快速存档"

    # renpy/common/00action_file.rpy:617
    old "File page [text]"
    new "第 [text] 页存档"

    # renpy/common/00action_file.rpy:675
    old "Page {}"
    new "第 {} 页"

    # renpy/common/00action_file.rpy:675
    old "Automatic saves"
    new "自动存档"

    # renpy/common/00action_file.rpy:675
    old "Quick saves"
    new "快速存档"

    # renpy/common/00action_file.rpy:816
    old "Next file page."
    new "下一页存档。"

    # renpy/common/00action_file.rpy:888
    old "Previous file page."
    new "上一页存档。"

    # renpy/common/00action_file.rpy:949
    old "Quick save complete."
    new "快速保存完成。"

    # renpy/common/00action_file.rpy:964
    old "Quick save."
    new "快速保存。"

    # renpy/common/00action_file.rpy:983
    old "Quick load."
    new "快速读取。"

    # renpy/common/00action_other.rpy:416
    old "Language [text]"
    new "语言 [text]"

    # renpy/common/00action_other.rpy:786
    old "Open [text] directory."
    new "打开 [text] 目录。"

    # renpy/common/00director.rpy:712
    old "The interactive director is not enabled here."
    new "交互式编导器未能在此处启动。"

    # renpy/common/00director.rpy:1512
    old "⬆"
    new "⬆"

    # renpy/common/00director.rpy:1518
    old "⬇"
    new "⬇"

    # renpy/common/00director.rpy:1582
    old "Done"
    new "完成"

    # renpy/common/00director.rpy:1592
    old "(statement)"
    new "（语句）"

    # renpy/common/00director.rpy:1593
    old "(tag)"
    new "（标签）"

    # renpy/common/00director.rpy:1594
    old "(attributes)"
    new "（属性）"

    # renpy/common/00director.rpy:1595
    old "(transform)"
    new "（变换）"

    # renpy/common/00director.rpy:1620
    old "(transition)"
    new "（转场）"

    # renpy/common/00director.rpy:1632
    old "(channel)"
    new "（轨道）"

    # renpy/common/00director.rpy:1633
    old "(filename)"
    new "（文件名）"

    # renpy/common/00director.rpy:1662
    old "Change"
    new "更改"

    # renpy/common/00director.rpy:1664
    old "Add"
    new "添加"

    # renpy/common/00director.rpy:1667
    old "Cancel"
    new "取消"

    # renpy/common/00director.rpy:1670
    old "Remove"
    new "移除"

    # renpy/common/00director.rpy:1705
    old "Statement:"
    new "语句："

    # renpy/common/00director.rpy:1726
    old "Tag:"
    new "标签："

    # renpy/common/00director.rpy:1742
    old "Attributes:"
    new "属性："

    # renpy/common/00director.rpy:1753
    old "Click to toggle attribute, right click to toggle negative attribute."
    new "点击切换属性，右键点击切换反面属性。"

    # renpy/common/00director.rpy:1765
    old "Transforms:"
    new "变换："

    # renpy/common/00director.rpy:1776
    old "Click to set transform, right click to add to transform list."
    new "点击设置变换，右键点击添加到变换列表。"

    # renpy/common/00director.rpy:1777
    old "Customize director.transforms to add more transforms."
    new "自定义 director.transforms 以添加更多变换"

    # renpy/common/00director.rpy:1789
    old "Behind:"
    new "置后于："

    # renpy/common/00director.rpy:1800
    old "Click to set, right click to add to behind list."
    new "点击设置，右键点击添加到置后（behind）列表。"

    # renpy/common/00director.rpy:1812
    old "Transition:"
    new "转场："

    # renpy/common/00director.rpy:1822
    old "Click to set."
    new "点击设置"

    # renpy/common/00director.rpy:1823
    old "Customize director.transitions to add more transitions."
    new "自定义 director.transitions 以添加更多过渡。"

    # renpy/common/00director.rpy:1835
    old "Channel:"
    new "轨道："

    # renpy/common/00director.rpy:1846
    old "Customize director.audio_channels to add more channels."
    new "自定义 director.audio_channels 以添加更多音频频道。"

    # renpy/common/00director.rpy:1858
    old "Audio Filename:"
    new "音频文件："

    # renpy/common/00gui.rpy:448
    old "Are you sure?"
    new "您确定吗？"

    # renpy/common/00gui.rpy:449
    old "Are you sure you want to delete this save?"
    new "您确定要删除此存档吗？"

    # renpy/common/00gui.rpy:450
    old "Are you sure you want to overwrite your save?"
    new "您确定要覆盖此存档吗？"

    # renpy/common/00gui.rpy:451
    old "Loading will lose unsaved progress.\nAre you sure you want to do this?"
    new "读取存档将会使未保存的进度丢失。\n您确定要继续吗？"

    # renpy/common/00gui.rpy:452
    old "Are you sure you want to quit?"
    new "您确定要退出吗？"

    # renpy/common/00gui.rpy:453
    old "Are you sure you want to return to the main menu?\nThis will lose unsaved progress."
    new "您确定要返回到标题界面吗？\n此操作将会使未保存的进度丢失。"

    # renpy/common/00gui.rpy:454
    old "Are you sure you want to continue where you left off?"
    new "您确定要从上次中断的地方继续吗？"

    # renpy/common/00gui.rpy:455
    old "Are you sure you want to end the replay?"
    new "您确定要结束回想吗？"

    # renpy/common/00gui.rpy:456
    old "Are you sure you want to begin skipping?"
    new "您确定要开始快进吗？"

    # renpy/common/00gui.rpy:457
    old "Are you sure you want to skip to the next choice?"
    new "您确定要直接快进到下个选项吗？"

    # renpy/common/00gui.rpy:458
    old "Are you sure you want to skip unseen dialogue to the next choice?"
    new "您确定要跳过未读对话，直接快进到下个选项吗？"

    # renpy/common/00gui.rpy:459
    old "This save was created on a different device. Maliciously constructed save files can harm your computer. Do you trust this save's creator and everyone who could have changed the file?"
    new "此存档是在其他设备上创建的。恶意构造的存档文件可能会对您的计算机造成损害。您是否信任此存档的创建者以及所有可能更改过该文件的人？"

    # renpy/common/00gui.rpy:460
    old "Do you trust the device the save was created on? You should only choose yes if you are the device's sole user."
    new "您是否信任创建此存档的设备？只有当您是设备的唯一用户时，才应选择是。"

    # renpy/common/00keymap.rpy:325
    old "Failed to save screenshot as %s."
    new "截图保存到以下位置时失败：%s"

    # renpy/common/00keymap.rpy:346
    old "Saved screenshot as %s."
    new "截图已保存到以下位置：%s"

    # renpy/common/00library.rpy:257
    old "Skip Mode"
    new "快进"

    # renpy/common/00library.rpy:344
    old "This program contains free software under a number of licenses, including the MIT License and GNU Lesser General Public License. A complete list of software, including links to full source code, can be found {a=https://www.renpy.org/l/license}here{/a}."
    new "本程序包含了由数个许可证授权的自由软件，包括 MIT 许可证和 GNU 宽松通用公共许可证。完整软件列表及源代码链接，请{a=https://www.renpy.org/l/license}访问此处{/a}。"

    # renpy/common/00preferences.rpy:295
    old "display"
    new "显示"

    # renpy/common/00preferences.rpy:315
    old "transitions"
    new "过渡"

    # renpy/common/00preferences.rpy:324
    old "skip transitions"
    new "跳过过渡"

    # renpy/common/00preferences.rpy:326
    old "video sprites"
    new "视频精灵"

    # renpy/common/00preferences.rpy:335
    old "show empty window"
    new "显示空窗口"

    # renpy/common/00preferences.rpy:344
    old "text speed"
    new "文本速度"

    # renpy/common/00preferences.rpy:352
    old "joystick"
    new "手柄"

    # renpy/common/00preferences.rpy:352
    old "joystick..."
    new "手柄..."

    # renpy/common/00preferences.rpy:359
    old "skip"
    new "跳过"

    # renpy/common/00preferences.rpy:362
    old "skip unseen [text]"
    new "跳过没见过的 [text]"

    # renpy/common/00preferences.rpy:367
    old "skip unseen text"
    new "跳过没见过的文本"

    # renpy/common/00preferences.rpy:369
    old "begin skipping"
    new "开头快进"

    # renpy/common/00preferences.rpy:373
    old "after choices"
    new "选项后"

    # renpy/common/00preferences.rpy:380
    old "skip after choices"
    new "选项后快进"

    # renpy/common/00preferences.rpy:382
    old "auto-forward time"
    new "自动推进时间"

    # renpy/common/00preferences.rpy:396
    old "auto-forward"
    new "自动推进"

    # renpy/common/00preferences.rpy:403
    old "Auto forward"
    new "自动推进"

    # renpy/common/00preferences.rpy:406
    old "auto-forward after click"
    new "单击后自动推进"

    # renpy/common/00preferences.rpy:415
    old "automatic move"
    new "自动移动"

    # renpy/common/00preferences.rpy:424
    old "wait for voice"
    new "等待朗读"

    # renpy/common/00preferences.rpy:433
    old "voice sustain"
    new "维持朗读"

    # renpy/common/00preferences.rpy:442
    old "self voicing"
    new "机器朗读"

    # renpy/common/00preferences.rpy:445
    old "self voicing enable"
    new "机器朗读已启用"

    # renpy/common/00preferences.rpy:447
    old "self voicing disable"
    new "机器朗读已禁用"

    # renpy/common/00preferences.rpy:451
    old "self voicing volume drop"
    new "机器朗读音量减小"

    # renpy/common/00preferences.rpy:459
    old "clipboard voicing"
    new "剪贴板朗读"

    # renpy/common/00preferences.rpy:462
    old "clipboard voicing enable"
    new "剪贴板朗读已启用"

    # renpy/common/00preferences.rpy:464
    old "clipboard voicing disable"
    new "剪贴板朗读已禁用"

    # renpy/common/00preferences.rpy:468
    old "debug voicing"
    new "调试朗读"

    # renpy/common/00preferences.rpy:471
    old "debug voicing enable"
    new "调试朗读已启用"

    # renpy/common/00preferences.rpy:473
    old "debug voicing disable"
    new "调试朗读已禁用"

    # renpy/common/00preferences.rpy:477
    old "emphasize audio"
    new "强调音频"

    # renpy/common/00preferences.rpy:486
    old "rollback side"
    new "回滚侧"

    # renpy/common/00preferences.rpy:496
    old "gl powersave"
    new "gl 省电"

    # renpy/common/00preferences.rpy:502
    old "gl framerate"
    new "gl 帧率"

    # renpy/common/00preferences.rpy:505
    old "gl tearing"
    new "gl 撕裂"

    # renpy/common/00preferences.rpy:508
    old "font transform"
    new "字体变换"

    # renpy/common/00preferences.rpy:511
    old "font size"
    new "字体大小"

    # renpy/common/00preferences.rpy:519
    old "font line spacing"
    new "字体间距"

    # renpy/common/00preferences.rpy:527
    old "system cursor"
    new "系统光标"

    # renpy/common/00preferences.rpy:536
    old "renderer menu"
    new "渲染器菜单"

    # renpy/common/00preferences.rpy:539
    old "accessibility menu"
    new "辅助功能菜单"

    # renpy/common/00preferences.rpy:542
    old "high contrast text"
    new "高对比度文本"

    # renpy/common/00preferences.rpy:551
    old "audio when minimized"
    new "最小化时的音频"

    # renpy/common/00preferences.rpy:560
    old "audio when unfocused"
    new "未聚焦时的音频"

    # renpy/common/00preferences.rpy:569
    old "web cache preload"
    new "网络缓存预加载"

    # renpy/common/00preferences.rpy:584
    old "voice after game menu"
    new "游戏菜单后语音"

    # renpy/common/00preferences.rpy:593
    old "restore window position"
    new "恢复窗口位置"

    # renpy/common/00preferences.rpy:602
    old "mono audio"
    new "mono audio"

    # renpy/common/00preferences.rpy:611
    old "font kerning"
    new "font kerning"

    # renpy/common/00preferences.rpy:619
    old "reset"
    new "重置"

    # renpy/common/00preferences.rpy:632
    old "main volume"
    new "主要音量"

    # renpy/common/00preferences.rpy:633
    old "music volume"
    new "音乐音量"

    # renpy/common/00preferences.rpy:634
    old "sound volume"
    new "声音音量"

    # renpy/common/00preferences.rpy:635
    old "voice volume"
    new "语音音量"

    # renpy/common/00preferences.rpy:636
    old "mute main"
    new "静音主要"

    # renpy/common/00preferences.rpy:637
    old "mute music"
    new "静音音乐"

    # renpy/common/00preferences.rpy:638
    old "mute sound"
    new "静音声音"

    # renpy/common/00preferences.rpy:639
    old "mute voice"
    new "静音语音"

    # renpy/common/00preferences.rpy:640
    old "mute all"
    new "静音全部"

    # renpy/common/00preferences.rpy:723
    old "Clipboard voicing enabled. Press 'shift+C' to disable."
    new "剪贴板朗读已启用。按 Shift+C 来禁用。"

    # renpy/common/00preferences.rpy:725
    old "Self-voicing would say \"[renpy.display.tts.last]\". Press 'alt+shift+V' to disable."
    new "机器朗读会朗读“[renpy.display.tts.last]”。按 Alt+Shift+V 来禁用。"

    # renpy/common/00preferences.rpy:727
    old "Self-voicing enabled. Press 'v' to disable."
    new "机器朗读已启用。按 V 来禁用。"

    # renpy/common/00speechbubble.rpy:420
    old "Speech Bubble Editor"
    new "对话气泡编辑器"

    # renpy/common/00speechbubble.rpy:425
    old "(hide)"
    new "(隐藏)"

    # renpy/common/00speechbubble.rpy:436
    old "(clear retained bubbles)"
    new "(清除保留的气泡)"

    # renpy/common/00sync.rpy:70
    old "Sync downloaded."
    new "同步下载完成。"

    # renpy/common/00sync.rpy:184
    old "Could not connect to the Ren'Py Sync server."
    new "无法连接到 Ren'Py 同步服务器。"

    # renpy/common/00sync.rpy:186
    old "The Ren'Py Sync server timed out."
    new "Ren'Py 同步服务器已超时。"

    # renpy/common/00sync.rpy:188
    old "An unknown error occurred while connecting to the Ren'Py Sync server."
    new "在连接到 Ren'Py 同步服务器时发生了未知错误。"

    # renpy/common/00sync.rpy:204
    old "The Ren'Py Sync server does not have a copy of this sync. The sync ID may be invalid, or it may have timed out."
    new "Ren'Py 同步服务器没有该同步副本。同步 ID 可能无效，或者可能已经超时。"

    # renpy/common/00sync.rpy:305
    old "Please enter the sync ID you generated.\nNever enter a sync ID you didn't create yourself."
    new "请输入您生成的同步 ID。\n切勿输入并非由您创建的同步 ID。"

    # renpy/common/00sync.rpy:324
    old "The sync ID is not in the correct format."
    new "同步 ID 的格式不正确。"

    # renpy/common/00sync.rpy:344
    old "The sync could not be decrypted."
    new "无法解密该同步。"

    # renpy/common/00sync.rpy:367
    old "The sync belongs to a different game."
    new "该同步属于另一款游戏。"

    # renpy/common/00sync.rpy:372
    old "The sync contains a file with an invalid name."
    new "该同步包含一个文件，其文件名无效。"

    # renpy/common/00sync.rpy:425
    old "This will upload your saves to the {a=https://sync.renpy.org}Ren'Py Sync Server{/a}.\nDo you want to continue?"
    new "此操作将把您的存档上传到 {a=https://sync.renpy.org}Ren'Py 同步服务器{/a} 。\n您想要继续吗？"

    # renpy/common/00sync.rpy:433
    old "Yes"
    new "确定"

    # renpy/common/00sync.rpy:434
    old "No"
    new "取消"

    # renpy/common/00sync.rpy:457
    old "Enter Sync ID"
    new "输入同步 ID"

    # renpy/common/00sync.rpy:468
    old "This will contact the {a=https://sync.renpy.org}Ren'Py Sync Server{/a}."
    new "此操作将联系 {a=https://sync.renpy.org}Ren'Py 同步服务器{/a}。"

    # renpy/common/00sync.rpy:498
    old "Sync Success"
    new "同步成功"

    # renpy/common/00sync.rpy:501
    old "The Sync ID is:"
    new "同步 ID 是："

    # renpy/common/00sync.rpy:507
    old "You can use this ID to download your save on another device.\nThis sync will expire in an hour.\nRen'Py Sync is supported by {a=https://www.renpy.org/sponsors.html}Ren'Py's Sponsors{/a}."
    new "你可以使用此 ID 在另一设备上下载您的存档。\n此同步将在一小时后失效。\nRen'Py 同步由{a=https://www.renpy.org/sponsors.html}Ren'Py 赞助者{/a}赞助。"

    # renpy/common/00sync.rpy:511
    old "Continue"
    new "继续"

    # renpy/common/00sync.rpy:536
    old "Sync Error"
    new "同步错误"

    # renpy/common/00translation.rpy:63
    old "Translation identifier: [identifier]"
    new "Translation identifier: [identifier]"

    # renpy/common/00translation.rpy:84
    old " translates [tl.filename]:[tl.linenumber]"
    new " translates [tl.filename]:[tl.linenumber]"

    # renpy/common/00translation.rpy:101
    old "\n{color=#fff}Copied to clipboard.{/color}"
    new "\n{color=#fff} 已复制到剪贴板。 {/color}"

    # renpy/common/00iap.rpy:231
    old "Contacting App Store\nPlease Wait..."
    new "正在连接 App Store\n请稍后……"

    # renpy/common/00updater.rpy:415
    old "No update methods found."
    new "未找到更新方法"

    # renpy/common/00updater.rpy:462
    old "Could not download file list: "
    new "无法下载文件列表："

    # renpy/common/00updater.rpy:465
    old "File list digest does not match."
    new "文件列表摘要不匹配"

    # renpy/common/00updater.rpy:675
    old "An error is being simulated."
    new "正在模拟一个错误。"

    # renpy/common/00updater.rpy:863
    old "Either this project does not support updating, or the update status file was deleted."
    new "此项目不支持更新，或者是更新状态文件已被删除。"

    # renpy/common/00updater.rpy:877
    old "This account does not have permission to perform an update."
    new "此帐号没有执行更新的权限。"

    # renpy/common/00updater.rpy:880
    old "This account does not have permission to write the update log."
    new "此帐号没有写入更新日志的权限。"

    # renpy/common/00updater.rpy:966
    old "Could not verify update signature."
    new "无法验证更新签名。"

    # renpy/common/00updater.rpy:1289
    old "The update file was not downloaded."
    new "更新文件未能下载。"

    # renpy/common/00updater.rpy:1307
    old "The update file does not have the correct digest - it may have been corrupted."
    new "更新文件校验失败。文件可能已损坏。"

    # renpy/common/00updater.rpy:1457
    old "While unpacking {}, unknown type {}."
    new "解压 {} 时出现错误，文件类型 {} 无法解压。"

    # renpy/common/00updater.rpy:1928
    old "Updater"
    new "更新程序"

    # renpy/common/00updater.rpy:1935
    old "An error has occurred:"
    new "发生错误："

    # renpy/common/00updater.rpy:1937
    old "Checking for updates."
    new "正在检查更新。"

    # renpy/common/00updater.rpy:1939
    old "This program is up to date."
    new "此程序已是最新版本。"

    # renpy/common/00updater.rpy:1941
    old "[u.version] is available. Do you want to install it?"
    new "[u.version] 现已可用。您希望现在安装吗？"

    # renpy/common/00updater.rpy:1943
    old "Preparing to download the updates."
    new "正在准备下载更新。"

    # renpy/common/00updater.rpy:1945
    old "Downloading the updates."
    new "正在下载更新。"

    # renpy/common/00updater.rpy:1947
    old "Unpacking the updates."
    new "正在解压更新。"

    # renpy/common/00updater.rpy:1949
    old "Finishing up."
    new "完成。"

    # renpy/common/00updater.rpy:1951
    old "The updates have been installed. The program will restart."
    new "此更新已安装。程序将重新启动。"

    # renpy/common/00updater.rpy:1953
    old "The updates have been installed."
    new "更新已安装。"

    # renpy/common/00updater.rpy:1955
    old "The updates were cancelled."
    new "更新已取消。"

    # renpy/common/00updater.rpy:1970
    old "Proceed"
    new "继续"

    # renpy/common/00updater.rpy:1986
    old "Preparing to download the game data."
    new "准备下载游戏数据"

    # renpy/common/00updater.rpy:1988
    old "Downloading the game data."
    new "下载游戏数据"

    # renpy/common/00updater.rpy:1990
    old "The game data has been downloaded."
    new "游戏数据已下载"

    # renpy/common/00updater.rpy:1992
    old "An error occurred when trying to download game data:"
    new "尝试下载游戏数据时发生错误："

    # renpy/common/00updater.rpy:1997
    old "This game cannot be run until the game data has been downloaded."
    new "在游戏数据下载完毕之前，该游戏无法运行"

    # renpy/common/00updater.rpy:2004
    old "Retry"
    new "重试"

    # renpy/common/00compat.rpy:456
    old "Fullscreen"
    new "全屏"

    # renpy/common/00gallery.rpy:676
    old "Image [index] of [count] locked."
    new "图片 [count] / [index] 尚未解锁。"

    # renpy/common/00gallery.rpy:696
    old "prev"
    new "上一页"

    # renpy/common/00gallery.rpy:697
    old "next"
    new "下一页"

    # renpy/common/00gallery.rpy:698
    old "slideshow"
    new "幻灯片"

    # renpy/common/00gallery.rpy:699
    old "return"
    new "返回"

    # renpy/common/00gltest.rpy:89
    old "Renderer"
    new "渲染器"

    # renpy/common/00gltest.rpy:91
    old "Automatically Choose"
    new "自动选择"

    # renpy/common/00gltest.rpy:96
    old "Force GL2 Renderer"
    new "强制使用 GL2 渲染器"

    # renpy/common/00gltest.rpy:101
    old "Force ANGLE2 Renderer"
    new "强制使用 ANGLE2 渲染器"

    # renpy/common/00gltest.rpy:106
    old "Force GLES2 Renderer"
    new "强制使用 GLES2 渲染器"

    # renpy/common/00gltest.rpy:110
    old "Gamepad"
    new "手柄"

    # renpy/common/00gltest.rpy:112
    old "Enable (No Blocklist)"
    new "启用（无阻止项）"

    # renpy/common/00gltest.rpy:126
    old "Calibrate"
    new "校准"

    # renpy/common/00gltest.rpy:135
    old "Powersave"
    new "省电模式"

    # renpy/common/00gltest.rpy:145
    old "Framerate"
    new "帧率"

    # renpy/common/00gltest.rpy:147
    old "Screen"
    new "基于显示器"

    # renpy/common/00gltest.rpy:151
    old "60"
    new "60"

    # renpy/common/00gltest.rpy:155
    old "30"
    new "30"

    # renpy/common/00gltest.rpy:159
    old "Tearing"
    new "允许画面撕裂"

    # renpy/common/00gltest.rpy:171
    old "Changes will take effect the next time this program is run."
    new "更改将在下次运行该程序时生效。"

    # renpy/common/00gltest.rpy:178
    old "Quit"
    new "退出"

    # renpy/common/00gltest.rpy:207
    old "Performance Warning"
    new "性能警告"

    # renpy/common/00gltest.rpy:212
    old "This computer is using software rendering."
    new "此计算机正在使用软件渲染。"

    # renpy/common/00gltest.rpy:214
    old "This game requires use of GL2 that can't be initialised."
    new "此游戏需要使用 GL2 渲染器，但其无法初始化。"

    # renpy/common/00gltest.rpy:216
    old "This computer has a problem displaying graphics: [problem]."
    new "此计算机显示图形时遇到问题：[problem]"

    # renpy/common/00gltest.rpy:220
    old "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display."
    new "图形驱动程序可能已过期或未能正确运行。这会导致图形显示缓慢或者错误。"

    # renpy/common/00gltest.rpy:224
    old "The {a=edit:1:log.txt}log.txt{/a} file may contain information to help you determine what is wrong with your computer."
    new "{a=edit:1:log.txt}log.txt{/a} 文件可能包含一些信息，这些信息可以帮助确定您的计算机出了什么问题。"

    # renpy/common/00gltest.rpy:229
    old "More details on how to fix this can be found in the {a=[url]}documentation{/a}."
    new "关于如何解决该问题的更多详情可以在{a=[url]}文档{/a}中找到。"

    # renpy/common/00gltest.rpy:234
    old "Continue, Show this warning again"
    new "继续，下次仍然显示该警告"

    # renpy/common/00gltest.rpy:238
    old "Continue, Don't show warning again"
    new "继续，不再显示该警告"

    # renpy/common/00gltest.rpy:246
    old "Change render options"
    new "更改渲染选项"

    # renpy/common/00gamepad.rpy:33
    old "Select Gamepad to Calibrate"
    new "选择要校准的手柄"

    # renpy/common/00gamepad.rpy:36
    old "No Gamepads Available"
    new "无可用手柄"

    # renpy/common/00gamepad.rpy:56
    old "Calibrating [name] ([i]/[total])"
    new "正在校准 [name]（[i]/[total]）"

    # renpy/common/00gamepad.rpy:60
    old "Press or move the '[control!s]' [kind]."
    new "按下或移动 '[control!s]' [kind]。"

    # renpy/common/00gamepad.rpy:70
    old "Skip (A)"
    new "跳过（A）"

    # renpy/common/00gamepad.rpy:73
    old "Back (B)"
    new "返回（B）"

    # renpy/common/_errorhandling.rpym:757
    old "Open"
    new "打开"

    # renpy/common/_errorhandling.rpym:759
    old "Opens the traceback.txt file in a text editor."
    new "在文本编辑器中打开追溯报告（traceback.txt）。"

    # renpy/common/_errorhandling.rpym:761
    old "Copy BBCode"
    new "复制 BBCode"

    # renpy/common/_errorhandling.rpym:763
    old "Copies the traceback.txt file to the clipboard as BBcode for forums like https://lemmasoft.renai.us/."
    new "复制追溯报告（traceback.txt）的内容到剪贴板并转换为 BBcode 以供诸如 https://lemmasoft.renai.us/ 等论坛使用。"

    # renpy/common/_errorhandling.rpym:765
    old "Copy Markdown"
    new "复制 Markdown"

    # renpy/common/_errorhandling.rpym:767
    old "Copies the traceback.txt file to the clipboard as Markdown for Discord."
    new "复制追溯报告（traceback.txt）的内容到剪贴板并转换为 Markdown 以供 Discord 使用。"

    # renpy/common/_errorhandling.rpym:799
    old "An exception has occurred."
    new "发生异常。"

    # renpy/common/_errorhandling.rpym:828
    old "Rollback"
    new "回滚"

    # renpy/common/_errorhandling.rpym:830
    old "Attempts a roll back to a prior time, allowing you to save or choose a different choice."
    new "尝试回滚到先前的状态，使您可以存档或者选择不同选项。"

    # renpy/common/_errorhandling.rpym:833
    old "Ignore"
    new "忽略"

    # renpy/common/_errorhandling.rpym:837
    old "Ignores the exception, allowing you to continue."
    new "忽略异常，让您可以继续。"

    # renpy/common/_errorhandling.rpym:839
    old "Ignores the exception, allowing you to continue. This often leads to additional errors."
    new "忽略异常，让您可以继续。但这通常会引起更多错误。"

    # renpy/common/_errorhandling.rpym:843
    old "Reload"
    new "重新加载"

    # renpy/common/_errorhandling.rpym:845
    old "Reloads the game from disk, saving and restoring game state if possible."
    new "从硬盘重新加载游戏，尝试保存和恢复游戏状态。"

    # renpy/common/_errorhandling.rpym:848
    old "Console"
    new "控制台"

    # renpy/common/_errorhandling.rpym:850
    old "Opens a console to allow debugging the problem."
    new "打开控制台，允许您对问题进行调试。"

    # renpy/common/_errorhandling.rpym:863
    old "Quits the game."
    new "退出游戏。"

    # renpy/common/_errorhandling.rpym:885
    old "Parsing the script failed."
    new "解析脚本失败。"

    # renpy/common/_developer/developer.rpym:39
    old "Developer Menu"
    new "开发者菜单"

    # renpy/common/_developer/developer.rpym:44
    old "Interactive Director (D)"
    new "交互式编导器 (D)"

    # renpy/common/_developer/developer.rpym:46
    old "Reload Game (Shift+R)"
    new "重新加载游戏 (Shift+R)"

    # renpy/common/_developer/developer.rpym:48
    old "Console (Shift+O)"
    new "控制台 (Shift+O)"

    # renpy/common/_developer/developer.rpym:50
    old "Variable Viewer"
    new "变量查看器"

    # renpy/common/_developer/developer.rpym:52
    old "Persistent Viewer"
    new "持久化数据查看器"

    # renpy/common/_developer/developer.rpym:54
    old "Image Location Picker"
    new "图像坐标选取器"

    # renpy/common/_developer/developer.rpym:56
    old "Filename List"
    new "文件列表"

    # renpy/common/_developer/developer.rpym:60
    old "Show Image Load Log (F4)"
    new "显示图像加载日志 (F4)"

    # renpy/common/_developer/developer.rpym:63
    old "Hide Image Load Log (F4)"
    new "隐藏图像加载日志 (F4)"

    # renpy/common/_developer/developer.rpym:66
    old "Image Attributes"
    new "图像属性"

    # renpy/common/_developer/developer.rpym:70
    old "Show Translation Info"
    new "Show Translation Info"

    # renpy/common/_developer/developer.rpym:73
    old "Hide Translation Info"
    new "Hide Translation Info"

    # renpy/common/_developer/developer.rpym:78
    old "Speech Bubble Editor (Shift+B)"
    new "对话气泡编辑器（Shift+B）"

    # renpy/common/_developer/developer.rpym:82
    old "Show Filename and Line"
    new "显示文件名和行"

    # renpy/common/_developer/developer.rpym:85
    old "Hide Filename and Line"
    new "隐藏文件名和行"

    # renpy/common/_developer/developer.rpym:141
    old "Layer [l]:"
    new "层  [l] ："

    # renpy/common/_developer/developer.rpym:144
    old "    (transforms: [', '.join(transform_list)])"
    new "    (transforms: [', '.join(transform_list)])"

    # renpy/common/_developer/developer.rpym:148
    old "    [name!q] [attributes!q] (hidden)"
    new "    [name!q] [attributes!q] (hidden)"

    # renpy/common/_developer/developer.rpym:152
    old "    [name!q] [attributes!q]"
    new "    [name!q] [attributes!q]"

    # renpy/common/_developer/developer.rpym:205
    old "Nothing to inspect."
    new "没有可查验的对象。"

    # renpy/common/_developer/developer.rpym:216
    old "Hide deleted"
    new "隐藏已删除项"

    # renpy/common/_developer/developer.rpym:216
    old "Show deleted"
    new "显示已删除项"

    # renpy/common/_developer/developer.rpym:367
    old "Rectangle copied to clipboard."
    new "矩形坐标已复制到剪贴板。"

    # renpy/common/_developer/developer.rpym:370
    old "Position copied to clipboard."
    new "鼠标坐标已复制到剪贴板。"

    # renpy/common/_developer/developer.rpym:382
    old "Rectangle: %r"
    new "矩形坐标：%r"

    # renpy/common/_developer/developer.rpym:385
    old "Mouse position: %r"
    new "鼠标坐标：%r"

    # renpy/common/_developer/developer.rpym:390
    old "Right-click or escape to quit."
    new "右键点击或按下 Esc 键来退出。"

    # renpy/common/_developer/developer.rpym:440
    old "Type to filter: "
    new "输入关键字筛选："

    # renpy/common/_developer/developer.rpym:556
    old "Textures: [tex_count] ([tex_size_mb:.1f] MB)"
    new "贴图：[tex_count] ([tex_size_mb:.1f] MB)"

    # renpy/common/_developer/developer.rpym:560
    old "Image cache: [cache_pct:.1f]% ([cache_size_mb:.1f] MB)"
    new "图像缓存：[cache_pct:.1f]% ([cache_size_mb:.1f] MB)"

    # renpy/common/_developer/developer.rpym:570
    old "✔ "
    new "✔ "

    # renpy/common/_developer/developer.rpym:573
    old "✘ "
    new "✘ "

    # renpy/common/_developer/developer.rpym:578
    old "\n{color=#cfc}✔ predicted image (good){/color}\n{color=#fcc}✘ unpredicted image (bad){/color}\n{color=#fff}Drag to move.{/color}"
    new "\n{color=#cfc}✔ 已预载图像（良好）{/color}\n{color=#fcc}✘ 未预载图像（糟糕）{/color}\n{color=#fff}拖曳来移动位置。{/color}"

    # renpy/common/_developer/developer.rpym:628
    old "Click to open in editor."
    new "单击以在编辑器中打开。"

    # renpy/common/_developer/inspector.rpym:39
    old "Displayable Inspector"
    new "可视组件查验器"

    # renpy/common/_developer/inspector.rpym:62
    old "Size"
    new "大小"

    # renpy/common/_developer/inspector.rpym:66
    old "Style"
    new "样式"

    # renpy/common/_developer/inspector.rpym:72
    old "Location"
    new "源码位置"

    # renpy/common/_developer/inspector.rpym:124
    old "Inspecting Styles of [displayable_name!q]"
    new "正在查验 [displayable_name!q] 的样式"

    # renpy/common/_developer/inspector.rpym:141
    old "displayable:"
    new "可视组件："

    # renpy/common/_developer/inspector.rpym:147
    old "        (no properties affect the displayable)"
    new "        （无任何属性影响可视组件）"

    # renpy/common/_developer/inspector.rpym:149
    old "        (default properties omitted)"
    new "        （已省略默认属性）"

    # renpy/common/_developer/inspector.rpym:187
    old "<repr() failed>"
    new "<repr() 失败>"

    # renpy/common/00console.rpy:552
    old "Press <esc> to exit console. Type help for help.\n"
    new "按 Esc 来退出控制台。输入 help 来查看帮助。\n"

    # renpy/common/00console.rpy:556
    old "Ren'Py script enabled."
    new "Ren'Py 脚本已启用。"

    # renpy/common/00console.rpy:558
    old "Ren'Py script disabled."
    new "Ren'Py 脚本已禁用。"

    # renpy/common/00console.rpy:741
    old "The console is using short representations. To disable this, type 'long', and to re-enable, type 'short'"
    new "The console is using short representations. To disable this, type 'long', and to re-enable, type 'short'"

    # renpy/common/00console.rpy:810
    old "help: show this help\n help <expr>: show signature and documentation of <expr>"
    new "help：显示此帮助信息\n help <表达式>：显示 <表达式> 的签名和文档"

    # renpy/common/00console.rpy:834
    old "Help may display undocumented functions. Please check that the function or\nclass you want to use is documented.\n\n"
    new "帮助功能可能会显示未记录文档的函数。请确认您想使用的函数或类是否已经被记录文档。\n\n"

    # renpy/common/00console.rpy:843
    old "commands:\n"
    new "命令：\n"

    # renpy/common/00console.rpy:853
    old " <renpy script statement>: run the statement\n"
    new " <renpy 脚本语句>：运行此语句\n"

    # renpy/common/00console.rpy:855
    old " <python expression or statement>: run the expression or statement"
    new " <python 表达式或语句>：运行此表达式或语句"

    # renpy/common/00console.rpy:863
    old "clear: clear the console history"
    new "clear：清除控制台历史记录"

    # renpy/common/00console.rpy:867
    old "exit: exit the console"
    new "exit：退出控制台"

    # renpy/common/00console.rpy:875
    old "stack: print the return stack"
    new "stack：打印返回栈"

    # renpy/common/00console.rpy:897
    old "load <slot>: loads the game from slot"
    new "load <档位>：读取该档位的存档"

    # renpy/common/00console.rpy:910
    old "save <slot>: saves the game in slot"
    new "save <档位>：存储存档到该档位"

    # renpy/common/00console.rpy:921
    old "reload: reloads the game, refreshing the scripts"
    new "reload：重新加载游戏，并刷新脚本"

    # renpy/common/00console.rpy:929
    old "watch <expression>: watch a python expression\n watch short: makes the representation of traced expressions short (default)\n watch long: makes the representation of traced expressions as is"
    new "watch <表达式>：监视该 python 表达式\n watch short：简短表征所跟踪的表达式（默认）\n watch long：按原样表征所跟踪的表达式"

    # renpy/common/00console.rpy:966
    old "unwatch <expression>: stop watching an expression"
    new "unwatch <表达式>：停止监视该表达式"

    # renpy/common/00console.rpy:1012
    old "unwatchall: stop watching all expressions"
    new "unwatchall：停止监视所有表达式"

    # renpy/common/00console.rpy:1033
    old "jump <label>: jumps to label"
    new "jump <标签>：跳转到此标签"

    # renpy/common/00console.rpy:1049
    old "short: Shorten the representation of objects on the console (default)."
    new "short：在控制台中缩短对象的表征（默认）。"

    # renpy/common/00console.rpy:1053
    old "long: Print the full representation of objects on the console."
    new "long：在控制台中打印出对象的完整表征。"

    # renpy/common/00console.rpy:1057
    old "escape: Enables escaping of unicode symbols in unicode strings."
    new "escape：启用转义 Unicode 字符串中的 Unicode 符号。"

    # renpy/common/00console.rpy:1061
    old "unescape: Disables escaping of unicode symbols in unicode strings and print it as is (default)."
    new "unescape：禁止转义 Unicode 字符串中的 Unicode 符号，并按原样打印（默认）。"

define gui.system_font = 'SourceHanSansLite.ttf'
