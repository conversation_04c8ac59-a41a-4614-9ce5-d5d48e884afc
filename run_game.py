#!/usr/bin/env python3
"""
LIFE游戏启动脚本
用于启动Ren'Py游戏或显示安装说明
"""

import os
import sys
import subprocess
import webbrowser
from pathlib import Path

def find_renpy():
    """查找系统中的Ren'Py安装"""
    possible_paths = [
        # Windows常见路径
        r"C:\Program Files\RenPy\renpy.exe",
        r"C:\Program Files (x86)\RenPy\renpy.exe",
        # 用户目录
        os.path.expanduser("~/renpy/renpy.exe"),
        os.path.expanduser("~/RenPy/renpy.exe"),
        # 当前目录
        "./renpy.exe",
        "../renpy.exe",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_renpy_project():
    """检查是否是有效的Ren'Py项目"""
    required_files = [
        "game/options.rpy",
        "game/script.rpy",
        "game/screens.rpy"
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            return False
    
    return True

def open_ui_preview():
    """打开UI预览页面"""
    preview_file = Path("ui_preview.html")
    if preview_file.exists():
        webbrowser.open(f"file://{preview_file.absolute()}")
        print("已在浏览器中打开UI预览页面")
        return True
    return False

def show_installation_guide():
    """显示安装指南"""
    print("""
=== LIFE游戏运行指南 ===

要运行这个Ren'Py游戏，您需要：

1. 下载Ren'Py SDK:
   访问 https://www.renpy.org/latest.html
   下载适合您操作系统的版本

2. 安装Ren'Py:
   - 解压下载的文件到任意目录
   - 运行 renpy.exe (Windows) 或 renpy.sh (Linux/Mac)

3. 打开项目:
   - 在Ren'Py启动器中点击"打开项目"
   - 选择本项目文件夹 (包含game文件夹的目录)
   - 点击"启动项目"

4. 或者查看UI预览:
   - 运行此脚本时会自动打开 ui_preview.html
   - 这是一个静态的界面预览版本

=== 项目文件说明 ===

game/
├── screens.rpy          # 主界面定义
├── life_ui_styles.rpy   # UI样式系统  
├── script.rpy           # 游戏逻辑
├── gui.rpy              # GUI配置
└── options.rpy          # 游戏选项

ui_preview.html          # 静态UI预览
README.md               # 详细说明文档

=== 技术支持 ===

如果遇到问题，请检查：
1. 是否正确安装了Ren'Py SDK
2. 项目文件是否完整
3. 查看 log.txt 文件中的错误信息

""")

def main():
    """主函数"""
    print("LIFE游戏启动器")
    print("=" * 50)
    
    # 检查项目文件
    if not check_renpy_project():
        print("错误: 项目文件不完整，请检查game文件夹中的必要文件")
        return 1
    
    print("✓ 项目文件检查通过")
    
    # 查找Ren'Py
    renpy_path = find_renpy()
    
    if renpy_path:
        print(f"✓ 找到Ren'Py: {renpy_path}")
        try:
            # 启动游戏
            print("正在启动游戏...")
            subprocess.run([renpy_path, "."], check=True)
            return 0
        except subprocess.CalledProcessError as e:
            print(f"启动游戏失败: {e}")
            print("尝试打开UI预览...")
    else:
        print("✗ 未找到Ren'Py安装")
    
    # 打开UI预览
    if open_ui_preview():
        print("\n提示: 要运行完整游戏，请安装Ren'Py SDK")
        show_installation_guide()
        return 0
    else:
        print("错误: 无法找到UI预览文件")
        show_installation_guide()
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n用户取消操作")
        sys.exit(0)
    except Exception as e:
        print(f"发生错误: {e}")
        sys.exit(1)
